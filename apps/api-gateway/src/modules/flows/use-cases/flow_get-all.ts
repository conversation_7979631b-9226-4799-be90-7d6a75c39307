import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService, Flow } from '../../../database';

@Injectable()
export class GetAllFlowsUseCase implements UseCase {
  private readonly logger = new Logger(GetAllFlowsUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute(): Promise<Flow[]> {
    this.logger.verbose({ msg: 'Started getting all flows' });

    const flows = await this.db.flows.find({
      // where: {
      //   // TODO: where project
      // },
    });

    this.logger.verbose({
      msg: 'All flows found',
      data: {
        flowsFound: flows,
      },
    });

    return flows;
  }
}
