import { Module } from '@nestjs/common';
import { DatabaseModule } from '@database';
import { FlowsController } from './flows.controller';
import { FlowsService } from './flows.service';
import {
  CreateFlowUseCase,
  DeleteFlowByIdUseCase,
  GetAllFlowsUseCase,
  GetFlowByIdUseCase,
  MoveFlowUseCase,
  UpdateFlowUseCase,
} from './use-cases';

@Module({
  imports: [DatabaseModule],
  providers: [
    FlowsService,

    CreateFlowUseCase,
    DeleteFlowByIdUseCase,
    GetAllFlowsUseCase,
    GetFlowByIdUseCase,
    MoveFlowUseCase,
    UpdateFlowUseCase,
  ],
  controllers: [FlowsController],
})
export class FlowsModule {}
