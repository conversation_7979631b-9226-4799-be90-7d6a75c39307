import { Injectable } from '@nestjs/common';
import { FlowModel } from '@libs/models';
import { Flow } from '../../database';

@Injectable()
export class FlowsService {
  constructor() {}

  /**
   * Removes undefined values from the values object and then assigns the values to the entity.
   * !!! This function mutates the entity
   */
  public normalizeEntityValues(entity: Flow, values: Partial<Flow | FlowModel>): Flow {
    // exclude undefined values
    const normalizedValues = { ...values };
    for (const key in normalizedValues) {
      if (normalizedValues[key] === undefined) {
        delete normalizedValues[key];
      }
    }

    // assign values
    Object.assign(entity, normalizedValues);
    if (entity.description === '') entity.description = null;

    return entity;
  }
}
