import { Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ZodValidationPipe } from 'nestjs-zod';
import { GlobalApiExceptionFilter, ZodResponseInterceptor } from '@libs/common/api';
import { EnvsModule, generateAppEnvFilePaths } from '@libs/common/envs';
import { PinoLoggerModule } from '@libs/common/logger';
import { RedisModule } from '@libs/redis';
import { EnvVariablesSchema } from './config';
// import { FlowProcessStepsModule } from './modules/flows__flow-process-steps/flow-process-steps.module';
// import { FlowProcessesModule } from './modules/flows__flow-processes/flow-processes.module';
import { FlowsModule } from './modules/flows/flows.module';

@Module({
  imports: [
    EnvsModule.forRoot({
      schema: EnvVariablesSchema,
      envFilePath: generateAppEnvFilePaths('api-gateway'),
    }),

    PinoLoggerModule.forRootAsync('easy-flow-core'),

    RedisModule.forRootAsync(),

    FlowsModule,
    // FlowProcessesModule,
    // FlowProcessStepsModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodResponseInterceptor,
    },
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalApiExceptionFilter,
    },
  ],
})
export class ApiGatewayModule {}
