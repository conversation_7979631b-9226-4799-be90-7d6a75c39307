import { applyDecorators, HttpStatus } from '@nestjs/common';
import { ApiProperty, ApiResponse, ApiResponseExamples, ApiResponseOptions } from '@nestjs/swagger';

export class ApiErrorDto {
  @ApiProperty()
  error: true;

  @ApiProperty({ enum: HttpStatus })
  statusCode: HttpStatus;

  @ApiProperty()
  message: string;

  @ApiProperty()
  cause: string;
}

export function ApiError(
  statusCode: HttpStatus,
  message: string,
  cause?: string,
  options?: ApiResponseOptions,
) {
  return applyDecorators(
    ApiResponse({
      ...options,
      status: statusCode,
      example: {
        error: true,
        statusCode,
        message,
        cause,
      },
      type: ApiErrorDto,
    }),
  );
}

export function ApiErrors(
  statusCode: HttpStatus,
  errors: { message: string; cause?: string }[],
  options?: ApiResponseOptions,
) {
  return applyDecorators(
    ApiResponse({
      ...options,
      status: statusCode,
      type: ApiErrorDto,
      examples: errors.reduce(
        (list, { message, cause }) => {
          list[message] = {
            value: {
              error: true,
              statusCode,
              message,
              cause,
            },
            // summary: '', // TODO
          };
          return list;
        },
        {} as Record<string, ApiResponseExamples>,
      ),
    }),
  );
}
