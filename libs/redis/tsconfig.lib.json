{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "baseUrl": ".",
    "rootDir": "src",
    "outDir": "dist",
    "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo",
    "emitDeclarationOnly": true,
    "forceConsistentCasingInFileNames": true,
    "types": ["node"],
    "target": "es2021",
    "strictNullChecks": true,
    "noImplicitAny": true,
    "strictBindCallApply": true,
    "noFallthroughCasesInSwitch": true

    // "module": "commonjs"
    // "declaration": true,
    // "removeComments": true,
    // "emitDecoratorMetadata": true,
    // "experimentalDecorators": true,
    // "allowSyntheticDefaultImports": true,
    // "target": "ES2023",
    // "sourceMap": true,
    // "incremental": true,
    // "skipLibCheck": true,
    // "strictNullChecks": true,
    // "forceConsistentCasingInFileNames": true,
    // "noImplicitAny": false,
    // "strictBindCallApply": false,
    // "noFallthroughCasesInSwitch": false,
    // "esModuleInterop": true
  },
  "include": ["src/**/*.ts", "../../env.d.ts"],
  "references": []
}
