{"extends": "../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "rootDir": "src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "emitDeclarationOnly": true, "forceConsistentCasingInFileNames": true, "types": ["node"], "target": "es2021", "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*.ts", "../../env.d.ts"], "references": []}