{"compilerOptions": {"composite": true, "declarationMap": true, "emitDeclarationOnly": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2021"], "module": "nodenext", "moduleResolution": "nodenext", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true, "target": "es2021", "customConditions": ["development"]}}